var u=Object.defineProperty;var g=(s,n)=>u(s,"name",{value:n,configurable:!0});let t=!0;const l=typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:{};let i=0;if(l.process&&l.process.env&&l.process.stdout){const{FORCE_COLOR:s,NODE_DISABLE_COLORS:n,NO_COLOR:r,TERM:o,COLORTERM:c}=l.process.env;n||r||s==="0"?t=!1:s==="1"||s==="2"||s==="3"?t=!0:o==="dumb"?t=!1:"CI"in l.process.env&&["TRAVIS","CIRCLECI","APPVEYOR","GITLAB_CI","GITHUB_ACTIONS","BUILDKITE","DRONE"].some(a=>a in l.process.env)?t=!0:t=process.stdout.isTTY,t&&(process.platform==="win32"||c&&(c==="truecolor"||c==="24bit")?i=3:o&&(o.endsWith("-256color")||o.endsWith("256"))?i=2:i=1)}let f={enabled:t,supportLevel:i};function e(s,n,r=1){const o=`\x1B[${s}m`,c=`\x1B[${n}m`,a=new RegExp(`\\x1b\\[${n}m`,"g");return p=>f.enabled&&f.supportLevel>=r?o+(""+p).replace(a,o)+c:""+p}g(e,"kolorist");const b=e(30,39),d=e(33,39),O=e(90,39),C=e(92,39),R=e(95,39),I=e(96,39),L=e(44,49),E=e(100,49),T=e(103,49);export{b as a,T as b,L as c,E as d,R as e,C as f,O as g,I as l,f as o,d as y};

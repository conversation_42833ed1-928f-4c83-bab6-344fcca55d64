/* auto-generated by NAPI-RS */
/* eslint-disable */
export declare function compress(input: string | Uint8Array, options?: EncOptions | undefined | null, signal?: AbortSignal | undefined | null): Promise<Buffer>

export declare function compressSync(input: string | Uint8Array, options?: EncOptions | undefined | null): Buffer

export interface DecOptions {
  asBuffer?: boolean
  /**
   * do not use `create_external_buffer` to create the output buffer

   * set this option to `true` will make the API slower

   * for compatibility with electron >= 21

   * see https://www.electronjs.org/blog/v8-memory-cage and https://github.com/electron/electron/issues/35801#issuecomment-1261206333
   */
  copyOutputData?: boolean
}

export interface EncOptions {
  /**
   * do not use `create_external_buffer` to create the output buffer

   * for compatibility with electron >= 21

   * set this option to `true` will make the API slower

   * see https://www.electronjs.org/blog/v8-memory-cage and https://github.com/electron/electron/issues/35801#issuecomment-1261206333
   */
  copyOutputData?: boolean
}

export declare function uncompress(input: string | Uint8Array, options?: DecOptions | undefined | null, signal?: AbortSignal | undefined | null): Promise<string | Buffer>

export declare function uncompressSync(input: string | Uint8Array, options?: DecOptions | undefined | null): string | Buffer

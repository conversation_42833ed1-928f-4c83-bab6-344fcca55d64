{"name": "@napi-rs/snappy-win32-x64-msvc", "version": "7.3.2", "cpu": ["x64"], "main": "snappy.win32-x64-msvc.node", "files": ["snappy.win32-x64-msvc.node"], "description": "Fastest Snappy compression library in Node.js", "keywords": ["snappy", "snap", "compression", "compress", "napi-rs", "NAPI", "N-API", "Rust", "Node-API", "node-addon", "node-addon-api"], "license": "MIT", "engines": {"node": ">= 10"}, "repository": "https://github.com/Brooooooklyn/snappy", "publishConfig": {"registry": "https://registry.npmjs.org/", "access": "public"}, "os": ["win32"]}
{"name": "async-exit-hook", "version": "2.0.1", "description": "Run some code when the process exits (supports async hooks and pm2 clustering)", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/tapppi/async-exit-hook.git"}, "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/tapppi"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://sindresorhus.com"}], "engines": {"node": ">=0.12.0"}, "scripts": {"test": "xo && nyc ava", "release": "standard-version"}, "files": ["index.js"], "keywords": ["exit", "quit", "process", "hook", "graceful", "handler", "shutdown", "sigterm", "sigint", "sighup", "pm2", "cluster", "child", "reload", "async", "terminate", "kill", "stop", "event"], "devDependencies": {"ava": "^0.21.0", "coveralls": "^2.11.14", "nyc": "^10.3.2", "standard-version": "^4.2.0", "xo": "^0.18.2"}, "ava": {"files": ["test/*.js", "!tests/cases/*"]}}
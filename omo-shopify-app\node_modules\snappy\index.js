"use strict";const{createRequire}=require("node:module");require=createRequire(__filename);const{readFileSync}=require("node:fs");let nativeBinding=null;const loadErrors=[],isMusl=()=>{let e=!1;return process.platform==="linux"&&(e=isMuslFromFilesystem(),e===null&&(e=isMuslFromReport()),e===null&&(e=isMuslFromChildProcess())),e},isFileMusl=e=>e.includes("libc.musl-")||e.includes("ld-musl-"),isMuslFromFilesystem=()=>{try{return readFileSync("/usr/bin/ldd","utf-8").includes("musl")}catch{return null}},isMuslFromReport=()=>{let e=null;return typeof process.report?.getReport=="function"&&(process.report.excludeNetwork=!0,e=process.report.getReport()),e?e.header&&e.header.glibcVersionRuntime?!1:!!(Array.isArray(e.sharedObjects)&&e.sharedObjects.some(isFileMusl)):null},isMuslFromChildProcess=()=>{try{return require("child_process").execSync("ldd --version",{encoding:"utf8"}).includes("musl")}catch{return!1}};function requireNative(){if(process.env.NAPI_RS_NATIVE_LIBRARY_PATH)try{nativeBinding=require(process.env.NAPI_RS_NATIVE_LIBRARY_PATH)}catch(e){loadErrors.push(e)}else if(process.platform==="android")if(process.arch==="arm64"){try{return require("./snappy.android-arm64.node")}catch(e){loadErrors.push(e)}try{const e=require("@napi-rs/snappy-android-arm64"),r=require("@napi-rs/snappy-android-arm64/package.json").version;if(r!=="7.3.2"&&process.env.NAPI_RS_ENFORCE_VERSION_CHECK&&process.env.NAPI_RS_ENFORCE_VERSION_CHECK!=="0")throw new Error(`Native binding package version mismatch, expected 7.3.2 but got ${r}. You can reinstall dependencies to fix this issue.`);return e}catch(e){loadErrors.push(e)}}else if(process.arch==="arm"){try{return require("./snappy.android-arm-eabi.node")}catch(e){loadErrors.push(e)}try{const e=require("@napi-rs/snappy-android-arm-eabi"),r=require("@napi-rs/snappy-android-arm-eabi/package.json").version;if(r!=="7.3.2"&&process.env.NAPI_RS_ENFORCE_VERSION_CHECK&&process.env.NAPI_RS_ENFORCE_VERSION_CHECK!=="0")throw new Error(`Native binding package version mismatch, expected 7.3.2 but got ${r}. You can reinstall dependencies to fix this issue.`);return e}catch(e){loadErrors.push(e)}}else loadErrors.push(new Error(`Unsupported architecture on Android ${process.arch}`));else if(process.platform==="win32")if(process.arch==="x64"){try{return require("./snappy.win32-x64-msvc.node")}catch(e){loadErrors.push(e)}try{const e=require("@napi-rs/snappy-win32-x64-msvc"),r=require("@napi-rs/snappy-win32-x64-msvc/package.json").version;if(r!=="7.3.2"&&process.env.NAPI_RS_ENFORCE_VERSION_CHECK&&process.env.NAPI_RS_ENFORCE_VERSION_CHECK!=="0")throw new Error(`Native binding package version mismatch, expected 7.3.2 but got ${r}. You can reinstall dependencies to fix this issue.`);return e}catch(e){loadErrors.push(e)}}else if(process.arch==="ia32"){try{return require("./snappy.win32-ia32-msvc.node")}catch(e){loadErrors.push(e)}try{const e=require("@napi-rs/snappy-win32-ia32-msvc"),r=require("@napi-rs/snappy-win32-ia32-msvc/package.json").version;if(r!=="7.3.2"&&process.env.NAPI_RS_ENFORCE_VERSION_CHECK&&process.env.NAPI_RS_ENFORCE_VERSION_CHECK!=="0")throw new Error(`Native binding package version mismatch, expected 7.3.2 but got ${r}. You can reinstall dependencies to fix this issue.`);return e}catch(e){loadErrors.push(e)}}else if(process.arch==="arm64"){try{return require("./snappy.win32-arm64-msvc.node")}catch(e){loadErrors.push(e)}try{const e=require("@napi-rs/snappy-win32-arm64-msvc"),r=require("@napi-rs/snappy-win32-arm64-msvc/package.json").version;if(r!=="7.3.2"&&process.env.NAPI_RS_ENFORCE_VERSION_CHECK&&process.env.NAPI_RS_ENFORCE_VERSION_CHECK!=="0")throw new Error(`Native binding package version mismatch, expected 7.3.2 but got ${r}. You can reinstall dependencies to fix this issue.`);return e}catch(e){loadErrors.push(e)}}else loadErrors.push(new Error(`Unsupported architecture on Windows: ${process.arch}`));else if(process.platform==="darwin"){try{return require("./snappy.darwin-universal.node")}catch(e){loadErrors.push(e)}try{const e=require("@napi-rs/snappy-darwin-universal"),r=require("@napi-rs/snappy-darwin-universal/package.json").version;if(r!=="7.3.2"&&process.env.NAPI_RS_ENFORCE_VERSION_CHECK&&process.env.NAPI_RS_ENFORCE_VERSION_CHECK!=="0")throw new Error(`Native binding package version mismatch, expected 7.3.2 but got ${r}. You can reinstall dependencies to fix this issue.`);return e}catch(e){loadErrors.push(e)}if(process.arch==="x64"){try{return require("./snappy.darwin-x64.node")}catch(e){loadErrors.push(e)}try{const e=require("@napi-rs/snappy-darwin-x64"),r=require("@napi-rs/snappy-darwin-x64/package.json").version;if(r!=="7.3.2"&&process.env.NAPI_RS_ENFORCE_VERSION_CHECK&&process.env.NAPI_RS_ENFORCE_VERSION_CHECK!=="0")throw new Error(`Native binding package version mismatch, expected 7.3.2 but got ${r}. You can reinstall dependencies to fix this issue.`);return e}catch(e){loadErrors.push(e)}}else if(process.arch==="arm64"){try{return require("./snappy.darwin-arm64.node")}catch(e){loadErrors.push(e)}try{const e=require("@napi-rs/snappy-darwin-arm64"),r=require("@napi-rs/snappy-darwin-arm64/package.json").version;if(r!=="7.3.2"&&process.env.NAPI_RS_ENFORCE_VERSION_CHECK&&process.env.NAPI_RS_ENFORCE_VERSION_CHECK!=="0")throw new Error(`Native binding package version mismatch, expected 7.3.2 but got ${r}. You can reinstall dependencies to fix this issue.`);return e}catch(e){loadErrors.push(e)}}else loadErrors.push(new Error(`Unsupported architecture on macOS: ${process.arch}`))}else if(process.platform==="freebsd")if(process.arch==="x64"){try{return require("./snappy.freebsd-x64.node")}catch(e){loadErrors.push(e)}try{const e=require("@napi-rs/snappy-freebsd-x64"),r=require("@napi-rs/snappy-freebsd-x64/package.json").version;if(r!=="7.3.2"&&process.env.NAPI_RS_ENFORCE_VERSION_CHECK&&process.env.NAPI_RS_ENFORCE_VERSION_CHECK!=="0")throw new Error(`Native binding package version mismatch, expected 7.3.2 but got ${r}. You can reinstall dependencies to fix this issue.`);return e}catch(e){loadErrors.push(e)}}else if(process.arch==="arm64"){try{return require("./snappy.freebsd-arm64.node")}catch(e){loadErrors.push(e)}try{const e=require("@napi-rs/snappy-freebsd-arm64"),r=require("@napi-rs/snappy-freebsd-arm64/package.json").version;if(r!=="7.3.2"&&process.env.NAPI_RS_ENFORCE_VERSION_CHECK&&process.env.NAPI_RS_ENFORCE_VERSION_CHECK!=="0")throw new Error(`Native binding package version mismatch, expected 7.3.2 but got ${r}. You can reinstall dependencies to fix this issue.`);return e}catch(e){loadErrors.push(e)}}else loadErrors.push(new Error(`Unsupported architecture on FreeBSD: ${process.arch}`));else if(process.platform==="linux")if(process.arch==="x64")if(isMusl()){try{return require("./snappy.linux-x64-musl.node")}catch(e){loadErrors.push(e)}try{const e=require("@napi-rs/snappy-linux-x64-musl"),r=require("@napi-rs/snappy-linux-x64-musl/package.json").version;if(r!=="7.3.2"&&process.env.NAPI_RS_ENFORCE_VERSION_CHECK&&process.env.NAPI_RS_ENFORCE_VERSION_CHECK!=="0")throw new Error(`Native binding package version mismatch, expected 7.3.2 but got ${r}. You can reinstall dependencies to fix this issue.`);return e}catch(e){loadErrors.push(e)}}else{try{return require("./snappy.linux-x64-gnu.node")}catch(e){loadErrors.push(e)}try{const e=require("@napi-rs/snappy-linux-x64-gnu"),r=require("@napi-rs/snappy-linux-x64-gnu/package.json").version;if(r!=="7.3.2"&&process.env.NAPI_RS_ENFORCE_VERSION_CHECK&&process.env.NAPI_RS_ENFORCE_VERSION_CHECK!=="0")throw new Error(`Native binding package version mismatch, expected 7.3.2 but got ${r}. You can reinstall dependencies to fix this issue.`);return e}catch(e){loadErrors.push(e)}}else if(process.arch==="arm64")if(isMusl()){try{return require("./snappy.linux-arm64-musl.node")}catch(e){loadErrors.push(e)}try{const e=require("@napi-rs/snappy-linux-arm64-musl"),r=require("@napi-rs/snappy-linux-arm64-musl/package.json").version;if(r!=="7.3.2"&&process.env.NAPI_RS_ENFORCE_VERSION_CHECK&&process.env.NAPI_RS_ENFORCE_VERSION_CHECK!=="0")throw new Error(`Native binding package version mismatch, expected 7.3.2 but got ${r}. You can reinstall dependencies to fix this issue.`);return e}catch(e){loadErrors.push(e)}}else{try{return require("./snappy.linux-arm64-gnu.node")}catch(e){loadErrors.push(e)}try{const e=require("@napi-rs/snappy-linux-arm64-gnu"),r=require("@napi-rs/snappy-linux-arm64-gnu/package.json").version;if(r!=="7.3.2"&&process.env.NAPI_RS_ENFORCE_VERSION_CHECK&&process.env.NAPI_RS_ENFORCE_VERSION_CHECK!=="0")throw new Error(`Native binding package version mismatch, expected 7.3.2 but got ${r}. You can reinstall dependencies to fix this issue.`);return e}catch(e){loadErrors.push(e)}}else if(process.arch==="arm")if(isMusl()){try{return require("./snappy.linux-arm-musleabihf.node")}catch(e){loadErrors.push(e)}try{const e=require("@napi-rs/snappy-linux-arm-musleabihf"),r=require("@napi-rs/snappy-linux-arm-musleabihf/package.json").version;if(r!=="7.3.2"&&process.env.NAPI_RS_ENFORCE_VERSION_CHECK&&process.env.NAPI_RS_ENFORCE_VERSION_CHECK!=="0")throw new Error(`Native binding package version mismatch, expected 7.3.2 but got ${r}. You can reinstall dependencies to fix this issue.`);return e}catch(e){loadErrors.push(e)}}else{try{return require("./snappy.linux-arm-gnueabihf.node")}catch(e){loadErrors.push(e)}try{const e=require("@napi-rs/snappy-linux-arm-gnueabihf"),r=require("@napi-rs/snappy-linux-arm-gnueabihf/package.json").version;if(r!=="7.3.2"&&process.env.NAPI_RS_ENFORCE_VERSION_CHECK&&process.env.NAPI_RS_ENFORCE_VERSION_CHECK!=="0")throw new Error(`Native binding package version mismatch, expected 7.3.2 but got ${r}. You can reinstall dependencies to fix this issue.`);return e}catch(e){loadErrors.push(e)}}else if(process.arch==="riscv64")if(isMusl()){try{return require("./snappy.linux-riscv64-musl.node")}catch(e){loadErrors.push(e)}try{const e=require("@napi-rs/snappy-linux-riscv64-musl"),r=require("@napi-rs/snappy-linux-riscv64-musl/package.json").version;if(r!=="7.3.2"&&process.env.NAPI_RS_ENFORCE_VERSION_CHECK&&process.env.NAPI_RS_ENFORCE_VERSION_CHECK!=="0")throw new Error(`Native binding package version mismatch, expected 7.3.2 but got ${r}. You can reinstall dependencies to fix this issue.`);return e}catch(e){loadErrors.push(e)}}else{try{return require("./snappy.linux-riscv64-gnu.node")}catch(e){loadErrors.push(e)}try{const e=require("@napi-rs/snappy-linux-riscv64-gnu"),r=require("@napi-rs/snappy-linux-riscv64-gnu/package.json").version;if(r!=="7.3.2"&&process.env.NAPI_RS_ENFORCE_VERSION_CHECK&&process.env.NAPI_RS_ENFORCE_VERSION_CHECK!=="0")throw new Error(`Native binding package version mismatch, expected 7.3.2 but got ${r}. You can reinstall dependencies to fix this issue.`);return e}catch(e){loadErrors.push(e)}}else if(process.arch==="ppc64"){try{return require("./snappy.linux-ppc64-gnu.node")}catch(e){loadErrors.push(e)}try{const e=require("@napi-rs/snappy-linux-ppc64-gnu"),r=require("@napi-rs/snappy-linux-ppc64-gnu/package.json").version;if(r!=="7.3.2"&&process.env.NAPI_RS_ENFORCE_VERSION_CHECK&&process.env.NAPI_RS_ENFORCE_VERSION_CHECK!=="0")throw new Error(`Native binding package version mismatch, expected 7.3.2 but got ${r}. You can reinstall dependencies to fix this issue.`);return e}catch(e){loadErrors.push(e)}}else if(process.arch==="s390x"){try{return require("./snappy.linux-s390x-gnu.node")}catch(e){loadErrors.push(e)}try{const e=require("@napi-rs/snappy-linux-s390x-gnu"),r=require("@napi-rs/snappy-linux-s390x-gnu/package.json").version;if(r!=="7.3.2"&&process.env.NAPI_RS_ENFORCE_VERSION_CHECK&&process.env.NAPI_RS_ENFORCE_VERSION_CHECK!=="0")throw new Error(`Native binding package version mismatch, expected 7.3.2 but got ${r}. You can reinstall dependencies to fix this issue.`);return e}catch(e){loadErrors.push(e)}}else loadErrors.push(new Error(`Unsupported architecture on Linux: ${process.arch}`));else if(process.platform==="openharmony")if(process.arch==="arm64"){try{return require("./snappy.openharmony-arm64.node")}catch(e){loadErrors.push(e)}try{const e=require("@napi-rs/snappy-openharmony-arm64"),r=require("@napi-rs/snappy-openharmony-arm64/package.json").version;if(r!=="7.3.2"&&process.env.NAPI_RS_ENFORCE_VERSION_CHECK&&process.env.NAPI_RS_ENFORCE_VERSION_CHECK!=="0")throw new Error(`Native binding package version mismatch, expected 7.3.2 but got ${r}. You can reinstall dependencies to fix this issue.`);return e}catch(e){loadErrors.push(e)}}else if(process.arch==="x64"){try{return require("./snappy.openharmony-x64.node")}catch(e){loadErrors.push(e)}try{const e=require("@napi-rs/snappy-openharmony-x64"),r=require("@napi-rs/snappy-openharmony-x64/package.json").version;if(r!=="7.3.2"&&process.env.NAPI_RS_ENFORCE_VERSION_CHECK&&process.env.NAPI_RS_ENFORCE_VERSION_CHECK!=="0")throw new Error(`Native binding package version mismatch, expected 7.3.2 but got ${r}. You can reinstall dependencies to fix this issue.`);return e}catch(e){loadErrors.push(e)}}else if(process.arch==="arm"){try{return require("./snappy.openharmony-arm.node")}catch(e){loadErrors.push(e)}try{const e=require("@napi-rs/snappy-openharmony-arm"),r=require("@napi-rs/snappy-openharmony-arm/package.json").version;if(r!=="7.3.2"&&process.env.NAPI_RS_ENFORCE_VERSION_CHECK&&process.env.NAPI_RS_ENFORCE_VERSION_CHECK!=="0")throw new Error(`Native binding package version mismatch, expected 7.3.2 but got ${r}. You can reinstall dependencies to fix this issue.`);return e}catch(e){loadErrors.push(e)}}else loadErrors.push(new Error(`Unsupported architecture on OpenHarmony: ${process.arch}`));else loadErrors.push(new Error(`Unsupported OS: ${process.platform}, architecture: ${process.arch}`))}if(nativeBinding=requireNative(),!nativeBinding||process.env.NAPI_RS_FORCE_WASI){try{nativeBinding=require("./snappy.wasi.cjs")}catch(e){process.env.NAPI_RS_FORCE_WASI&&loadErrors.push(e)}if(!nativeBinding)try{nativeBinding=require("@napi-rs/snappy-wasm32-wasi")}catch(e){process.env.NAPI_RS_FORCE_WASI&&loadErrors.push(e)}}if(!nativeBinding)throw loadErrors.length>0?new Error("Cannot find native binding. npm has a bug related to optional dependencies (https://github.com/npm/cli/issues/4828). Please try `npm i` again after removing both package-lock.json and node_modules directory.",{cause:loadErrors}):new Error("Failed to load native binding");module.exports=nativeBinding,module.exports.compress=nativeBinding.compress,module.exports.compressSync=nativeBinding.compressSync,module.exports.uncompress=nativeBinding.uncompress,module.exports.uncompressSync=nativeBinding.uncompressSync;

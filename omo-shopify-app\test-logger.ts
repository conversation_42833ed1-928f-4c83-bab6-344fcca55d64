// Test script to verify logger functionality with Loki integration
import logger from './app/logger.server.js';

console.log('Testing logger with Loki integration...');

// Test different log levels
logger.error('Test error message', { userId: 123, action: 'test' });
logger.warn('Test warning message', { component: 'logger-test' });
logger.info('Test info message', { timestamp: new Date().toISOString() });
logger.http('Test HTTP message', { method: 'GET', url: '/test', status: 200 });
logger.debug('Test debug message', { details: 'This is a debug log' });

// Test with error object
const testError = new Error('Test error object');
logger.error('Error with stack trace', testError);

console.log('Logger test completed. Check console, log files, and Loki for output.');

// Give some time for async operations to complete
setTimeout(() => {
  console.log('Test finished. Logs should be visible in:');
  console.log('1. Console output (above)');
  console.log('2. Log files in ./logs/ directory');
  console.log('3. Loki (accessible via <PERSON><PERSON> at http://monitoring.localhost)');
  process.exit(0);
}, 2000);

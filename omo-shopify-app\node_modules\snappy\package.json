{"name": "snappy", "version": "7.3.2", "description": "Fastest Snappy compression library in Node.js", "main": "index.js", "browser": "browser.js", "repository": "https://github.com/Brooooooklyn/snappy", "license": "MIT", "keywords": ["snappy", "snap", "compression", "compress", "napi-rs", "NAPI", "N-API", "Rust", "Node-API", "node-addon", "node-addon-api"], "files": ["index.d.ts", "index.js", "browser.js"], "napi": {"binaryName": "snappy", "packageName": "@napi-rs/snappy", "targets": ["x86_64-unknown-linux-gnu", "x86_64-apple-darwin", "x86_64-pc-windows-msvc", "x86_64-unknown-linux-musl", "aarch64-unknown-linux-gnu", "i686-pc-windows-msvc", "armv7-unknown-linux-gnueabihf", "aarch64-apple-darwin", "aarch64-linux-android", "arm-linux-androideabi", "x86_64-unknown-freebsd", "aarch64-unknown-linux-musl", "aarch64-pc-windows-msvc", "riscv64gc-unknown-linux-gnu", "s390x-unknown-linux-gnu", "powerpc64le-unknown-linux-gnu", "aarch64-unknown-linux-ohos", "wasm32-wasip1-threads"], "wasm": {"browser": {"fs": false}}}, "engines": {"node": ">= 10"}, "publishConfig": {"registry": "https://registry.npmjs.org/", "access": "public"}, "scripts": {"artifacts": "napi artifacts --output-dir artifacts --npm-dir ./npm", "bench": "node --import @oxc-node/core/register benchmark/bench.ts", "build": "napi build --platform --release", "build:debug": "napi build --platform", "format": "run-p format:source format:rs format:toml", "format:toml": "taplo format", "format:rs": "cargo fmt", "format:source": "prettier --config ./package.json --write .", "lint": "ox<PERSON>", "prepublishOnly": "napi prepublish -t npm && esbuild --minify --outfile=index.js --allow-overwrite index.js", "test": "ava", "test:mem": "node --expose-gc ./memory-leak-detect.mjs", "preversion": "napi build --platform && git add .", "version": "napi version && conventional-changelog -p angular -i CHANGELOG.md -s && git add CHANGELOG.md"}, "devDependencies": {"@napi-rs/cli": "^3.1.5", "@oxc-node/core": "^0.0.32", "@taplo/cli": "^0.7.0", "@types/node": "^24.3.0", "ava": "^6.4.1", "chalk": "^5.5.0", "conventional-changelog-cli": "^5.0.0", "esbuild": "^0.25.9", "husky": "^9.1.7", "legacy-snappy": "npm:snappy@6", "lint-staged": "^16.1.5", "npm-run-all2": "^8.0.4", "oxlint": "^1.11.2", "prettier": "^3.6.2", "pretty-bytes": "^7.0.1", "table": "^6.9.0", "tinybench": "^5.0.0", "typescript": "^5.9.2"}, "lint-staged": {"*.@(js|ts|tsx)": ["eslint -c .eslintrc.yml --fix"], "*.@(js|ts|tsx|yml|yaml|md|json)": ["prettier --write"]}, "ava": {"extensions": {"ts": "module"}, "nodeArguments": ["--import", "@oxc-node/core/register"], "timeout": "2m", "workerThreads": false, "environmentVariables": {"TS_NODE_PROJECT": "./tsconfig.json"}}, "prettier": {"printWidth": 120, "semi": false, "trailingComma": "all", "singleQuote": true, "arrowParens": "always"}, "funding": {"type": "github", "url": "https://github.com/sponsors/Brooooooklyn"}, "packageManager": "yarn@4.9.2", "exports": {".": {"import": "./index.js", "require": "./index.js", "types": "./index.d.ts", "browser": "./browser.js"}}, "optionalDependencies": {"@napi-rs/snappy-linux-x64-gnu": "7.3.2", "@napi-rs/snappy-darwin-x64": "7.3.2", "@napi-rs/snappy-win32-x64-msvc": "7.3.2", "@napi-rs/snappy-linux-x64-musl": "7.3.2", "@napi-rs/snappy-linux-arm64-gnu": "7.3.2", "@napi-rs/snappy-win32-ia32-msvc": "7.3.2", "@napi-rs/snappy-linux-arm-gnueabihf": "7.3.2", "@napi-rs/snappy-darwin-arm64": "7.3.2", "@napi-rs/snappy-android-arm64": "7.3.2", "@napi-rs/snappy-android-arm-eabi": "7.3.2", "@napi-rs/snappy-freebsd-x64": "7.3.2", "@napi-rs/snappy-linux-arm64-musl": "7.3.2", "@napi-rs/snappy-win32-arm64-msvc": "7.3.2", "@napi-rs/snappy-linux-riscv64-gnu": "7.3.2", "@napi-rs/snappy-linux-s390x-gnu": "7.3.2", "@napi-rs/snappy-linux-ppc64-gnu": "7.3.2", "@napi-rs/snappy-openharmony-arm64": "7.3.2", "@napi-rs/snappy-wasm32-wasi": "7.3.2"}}
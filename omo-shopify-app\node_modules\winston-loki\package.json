{"name": "winston-loki", "version": "6.1.3", "description": "A Winston transport for Grafana Loki", "keywords": ["winston", "winston-transport", "transport", "loki", "grafana", "logging", "plugin", "SRE", "site reliability engineering", "grafana loki"], "main": "index.js", "types": "index.d.ts", "scripts": {"build:proto": "./node_modules/.bin/pbjs -t static-module -w commonjs -o ./src/proto/index.js ./src/proto/logproto.proto && npm run lint:fix", "test": "./node_modules/.bin/standard && ./node_modules/.bin/jest", "lint": "./node_modules/.bin/eslint *", "lint:fix": "./node_modules/.bin/standard --fix", "coverage": "jest --coverage", "coveralls": "jest --coverage && cat ./test/coverage/lcov.info | coveralls"}, "standard": {"env": {"node": true, "jest": true}, "ignore": ["test/coverage", "src/proto/index.js"]}, "jest": {"coverageDirectory": "./test/coverage", "coveragePathIgnorePatterns": ["./node_modules/", "./test/", "./src/proto/index.js"]}, "author": "<PERSON><PERSON>", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/JaniAnttonen/winston-loki.git"}, "dependencies": {"async-exit-hook": "2.0.1", "btoa": "^1.2.1", "protobufjs": "^7.2.4", "url-polyfill": "^1.1.12", "winston-transport": "^4.3.0"}, "optionalDependencies": {"snappy": "^7.2.2"}, "devDependencies": {"coveralls": "^3.0.2", "eslint": "^8.8.0", "eslint-config-prettier": "^3.6.0", "eslint-config-standard": "^12.0.0", "eslint-plugin-import": "^2.14.0", "eslint-plugin-node": "^8.0.1", "eslint-plugin-prettier": "^3.0.1", "eslint-plugin-promise": "^4.0.1", "eslint-plugin-standard": "^4.0.0", "jest": "^27.4.7", "standard": "^16.0.4", "winston": "^3.5.1"}}
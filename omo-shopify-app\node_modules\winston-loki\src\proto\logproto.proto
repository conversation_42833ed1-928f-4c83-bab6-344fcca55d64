syntax = "proto3";

package logproto;

option go_package = "github.com/grafana/loki/pkg/logproto";

import "google/protobuf/timestamp.proto";
import "github.com/gogo/protobuf/gogoproto/gogo.proto";

service Pusher {
    rpc Push(PushRequest) returns (PushResponse) {};
}

service Querier {
    rpc Query(QueryRequest) returns (stream QueryResponse) {};
    rpc Label(LabelRequest) returns (LabelResponse) {};
    rpc Tail(TailRequest) returns (stream TailResponse) {};
    rpc Series(SeriesRequest) returns (SeriesResponse) {};
    rpc TailersCount(TailersCountRequest) returns (TailersCountResponse) {};
}

service Ingester {
    rpc TransferChunks(stream TimeSeriesChunk) returns (TransferChunksResponse) {};
}

message PushRequest {
    repeated Stream streams = 1 [(gogoproto.jsontag) = "streams"];
}

message PushResponse {
}

message QueryRequest {
    string selector = 1;
    uint32 limit = 2;
    google.protobuf.Timestamp start = 3 [(gogoproto.stdtime) = true, (gogoproto.nullable) = false];
    google.protobuf.Timestamp end = 4 [(gogoproto.stdtime) = true, (gogoproto.nullable) = false];
    Direction direction = 5;
    reserved 6;
}

enum Direction {
    FORWARD = 0;
    BACKWARD = 1;
}

message QueryResponse {
    repeated Stream streams = 1;
}

message LabelRequest {
    string name = 1;
    bool values = 2; // True to fetch label values, false for fetch labels names.
    google.protobuf.Timestamp start = 3 [(gogoproto.stdtime) = true, (gogoproto.nullable) = true];
    google.protobuf.Timestamp end = 4 [(gogoproto.stdtime) = true, (gogoproto.nullable) = true];
}

message LabelResponse {
    repeated string values = 1;
}

message Stream {
    string labels = 1 [(gogoproto.jsontag) = "labels"];
    repeated Entry entries = 2 [(gogoproto.nullable) = false, (gogoproto.jsontag) = "entries"];
}

message Entry {
    google.protobuf.Timestamp timestamp = 1 [(gogoproto.stdtime) = true, (gogoproto.nullable) = false, (gogoproto.jsontag) = "ts"];
    string line = 2 [(gogoproto.jsontag) = "line"];
}

message TailRequest {
    string query = 1;
    reserved 2;
    uint32 delayFor = 3;
    uint32 limit = 4;
    google.protobuf.Timestamp start = 5 [(gogoproto.stdtime) = true, (gogoproto.nullable) = false];
}

message TailResponse {
    Stream stream = 1;
    repeated DroppedStream droppedStreams = 2;
}

message SeriesRequest {
    google.protobuf.Timestamp start = 1 [(gogoproto.stdtime) = true, (gogoproto.nullable) = false];
    google.protobuf.Timestamp end = 2 [(gogoproto.stdtime) = true, (gogoproto.nullable) = false];
    repeated string groups = 3;
}

message SeriesResponse {
    repeated SeriesIdentifier series = 1 [(gogoproto.nullable) = false];
}

message SeriesIdentifier {
    map<string,string> labels = 1;
}

message DroppedStream {
    google.protobuf.Timestamp from = 1 [(gogoproto.stdtime) = true, (gogoproto.nullable) = false];
    google.protobuf.Timestamp to = 2 [(gogoproto.stdtime) = true, (gogoproto.nullable) = false];
    string labels = 3;
}

message TimeSeriesChunk {
    string from_ingester_id = 1;
    string user_id = 2;
    repeated LabelPair labels = 3;
    repeated Chunk chunks = 4;
}

message LabelPair {
    string name = 1;
    string value = 2;
}

message Chunk {
    bytes data = 1;
}

message TransferChunksResponse {

}

message TailersCountRequest {

}

message TailersCountResponse {
    uint32 count = 1;
}
